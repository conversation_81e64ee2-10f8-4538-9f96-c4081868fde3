import React, { useState, useEffect } from 'react';
import { Search, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useGetBookingsByCustomerQuery, Booking } from '@/store/api/apiSlice';
import UpcomingSchedule from '@/components/UpcomingSchedule';
import ResponsiveLoader from '@/components/Loader';
import Footer from '@/components/Footer';
import { handleApiError } from '@/utils/toast';

function Schedule() {
  const navigate = useNavigate();

  const userId = localStorage.getItem('userId');
  const {
    data: bookingResponse,
    isLoading: bookingLoading,
    error: bookingError,
  } = useGetBookingsByCustomerQuery(userId, {
    skip: !userId,
    pollingInterval: 60000,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });
  const [selectedTab, setSelectedTab] = useState<
    'Pending' | 'Upcoming' | 'Completed' | 'Declined' | 'Cancelled'
  >('Pending');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (bookingError) {
      console.error('Bookings fetch error:', bookingError);

      const errorMessage = bookingError?.data?.error || bookingError?.message;
      const statusCode = bookingError?.status;

      handleApiError(bookingError, {
        401: 'Session expired. Please log in again.',
        403: 'Access denied. Please check your permissions.',
        404: 'Bookings not found. Please try refreshing.',
        [statusCode]:
          errorMessage || 'Failed to load bookings. Please try again.',
      });
    }
  }, [bookingError]);

  const getStatusForTab = (tab: string) => {
    switch (tab) {
      case 'Pending':
        return ['Pending'];
      case 'Upcoming':
        return ['Accepted'];
      case 'Completed':
        return ['Completed'];
      case 'Cancelled':
        return ['Cancelled', 'Declined'];
      default:
        return [];
    }
  };
  const filteredBookings =
    bookingResponse?.bookings?.filter((booking: Booking) => {
      // Filter by booking status
      const statusMatch = getStatusForTab(selectedTab).includes(
        booking.booking_status
      );

      // For pending bookings, also filter by date (only show today and future dates)
      if (selectedTab === 'Pending' && statusMatch) {
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Set to start of today
        const bookingDate = new Date(booking.booked_date);
        bookingDate.setHours(0, 0, 0, 0); // Set to start of booking date

        return bookingDate >= today;
      }

      return statusMatch;
    }) || [];

  const sortedBookings = filteredBookings.sort(
    (a, b) =>
      new Date(a.booked_date).getTime() - new Date(b.booked_date).getTime()
  );

  const displayedBookings = sortedBookings.filter((booking: Booking) =>
    booking.nurse_given_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Debug: Log booking data to check if profile_image_signed_url is present
  console.log('Schedule - Booking data:', displayedBookings.map(booking => ({
    nurse_cognitoId: booking.nurse_cognitoId,
    nurse_given_name: booking.nurse_given_name,
    profile_image_signed_url: booking.profile_image_signed_url,
    hasProfileImage: !!booking.profile_image_signed_url
  })));

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      {}
      <header className='relative w-full overflow-hidden text-white p-8 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed '>
          <img
            src='/Images/bg4.png'
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
        <div className=' absolute z-10 flex items-center top-5 '>
          <button onClick={() => navigate(-1)} className='mr-4'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>Bookings</h1>
        </div>
      </header>

      <div className='w-11/12 md:w-10/12 mx-auto min-h-screen'>
        {}
        <div className='flex md:w-6/12 w-full mx-auto bg-[#F2F2F2] rounded-lg p-1 md:mb-5 mb-4 mt-5'>
          <button
            className={`flex-1 py-2 px-2 text-sm font-medium ${
              selectedTab === 'Pending'
                ? 'bg-nursery-blue rounded-md text-white'
                : 'text-gray-600'
            }`}
            onClick={() => setSelectedTab('Pending')}
          >
            Pending
          </button>
          <button
            className={`flex-1 py-2 px-2 text-sm font-medium ${
              selectedTab === 'Upcoming'
                ? 'bg-nursery-blue rounded-md text-white'
                : 'text-gray-600'
            }`}
            onClick={() => setSelectedTab('Upcoming')}
          >
            Upcoming
          </button>
          <button
            className={`flex-1 py-2 px-2 text-sm font-medium ${
              selectedTab === 'Completed'
                ? 'bg-nursery-blue rounded-md text-white'
                : 'text-gray-600'
            }`}
            onClick={() => setSelectedTab('Completed')}
          >
            Completed
          </button>
          <button
            className={`flex-1 py-2 px-2 text-sm font-medium ${
              selectedTab === 'Cancelled'
                ? 'bg-nursery-blue rounded-md text-white'
                : 'text-gray-600'
            }`}
            onClick={() => setSelectedTab('Cancelled')}
          >
            Cancelled
          </button>
        </div>

        {}
        <div className='md:mb-6 mb-4 '>
          <div className='relative md:w-6/12 w-full mx-auto'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5' />
            <input
              type='text'
              placeholder='Search by nurse name'
              className='w-full pl-10 pr-4 py-3 border-gray-300 bg-[#F2F2F2] rounded-md text-sm text-gray-800 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-nursery-blue'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className='md:pb-8 pb-4'>
          {}
          {bookingLoading ? (
            <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
              <ResponsiveLoader />
            </div>
          ) : bookingError ? (
            <div className='bg-red-50 p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
              <p className='text-base font-medium text-red-500'>
                Failed to load Bookings
              </p>
              <p className='text-sm text-red-400'>Please try again later</p>
            </div>
          ) : displayedBookings.length > 0 ? (
            <div className='md:mb-0 mb-[70px]'>
              <div className='space-y-4'>
                {displayedBookings.map((booking, index) => (
                  <UpcomingSchedule
                    key={booking.id || index}
                    booking={booking}
                  />
                ))}
              </div>
            </div>
          ) : (
            <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
              <img
                src='/Images/calender.svg'
                alt='NO Bookings for you'
                className='p-2 w-20'
              />
              <p className='text-base font-medium text-slate-500'>
                No {selectedTab.toLowerCase()} Bookings for you
              </p>
            </div>
          )}
        </div>
      </div>
      {}
      <Footer />
    </div>
  );
}

export default Schedule;
