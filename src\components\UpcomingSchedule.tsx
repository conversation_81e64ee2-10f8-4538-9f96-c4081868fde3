import {
  Calendar,
  Clock,
  MapPin,
  MessageCircle,
  User,
  MoreVertical,
  Star,
  CheckCircle2,
  OctagonX,
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useState, useEffect, useRef } from 'react';
import {
  useGetCustomerServiceStatusesQuery,
  StatusBadgeProps,
  ActionDropdownProps,
  CancellationReasonProps,
  MessageButtonProps,
  ViewMoreButtonProps,
  PaymentButtonProps,
  ServiceTimelineProps,
  TimelineStepProps,
  ReviewSectionProps,
  UpcomingScheduleProps,
} from '@/store/api/apiSlice';

import { motion, AnimatePresence } from 'framer-motion';
import { handleApiError } from '@/utils/toast';
import NotificationBadge from '@/components/NotificationBadge';
import { useUnreadMessages } from '@/hooks/useUnreadMessages';

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    weekday: 'long',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };
  return date.toLocaleDateString('en-US', options);
};

const formatTime = (timeString: string) => {
  if (!timeString) return '';
  const [hours, minutes] = timeString.split(':');
  const hour12 = parseInt(hours) % 12 || 12;
  const ampm = parseInt(hours) < 12 ? 'am' : 'pm';
  return `${hour12}:${minutes} ${ampm}`;
};

const getShortAddress = (address: string) => {
  if (!address) return '';
  const words = address.split(' ');
  return words.slice(0, 4).join(' ') + (words.length > 4 ? '...' : '');
};

const formatDateShort = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString('en-US', { month: 'short' });
  return `${day} ${month.substring(0, 3)}`;
};

const getBookingServiceStatus = (serviceStatusData, bookingId) => {
  if (!serviceStatusData?.data) return null;

  if (Array.isArray(serviceStatusData.data)) {
    return serviceStatusData.data.find(
      status => status.booking_id === bookingId
    );
  }

  return serviceStatusData.data.booking_id === bookingId
    ? serviceStatusData.data
    : null;
};

const isBookingActionable = status => {
  return status === 'Pending' || status === 'Accepted';
};

const isBookingActiveOrCompleted = status => {
  return status === 'Accepted' || status === 'Completed';
};

const isBookingCancelledOrDeclined = status => {
  return status === 'Declined' || status === 'Cancelled';
};

const shouldShowServiceStatus = status => {
  return status === 'Accepted' || status === 'Completed';
};

const getStatusColor = status => {
  const statusColors = {
    Pending: 'bg-[#FFBB00]',
    Accepted: 'bg-[#00A912]',
    Completed: 'bg-[#00A912]',
    Declined: 'bg-[#EB001B]',
    Cancelled: 'bg-[#EB001B]',
  };
  return statusColors[status] || 'bg-gray-400';
};

const StatusBadge = ({ bookingStatus, badgeColor }: StatusBadgeProps) => (
  <span
    className={`px-3 py-1 text-white text-sm font-medium rounded-full ${badgeColor}`}
  >
    {bookingStatus || 'Scheduled'}
  </span>
);

const ActionDropdown = ({
  bookingStatus,
  showDropdown,
  setShowDropdown,
  dropdownRef,
  onCancelClick,
}: ActionDropdownProps) => {
  if (!isBookingActionable(bookingStatus)) return null;

  const getCancelText = () => {
    return bookingStatus === 'Pending' ? 'Cancel Request' : 'Cancel Booking';
  };

  return (
    <div className='relative' ref={dropdownRef}>
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className='p-1 hover:bg-gray-200 rounded-full transition-colors'
      >
        <MoreVertical className='w-5 h-5 text-gray-600' />
      </button>

      {showDropdown && (
        <div className='absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[140px]'>
          <button
            onClick={onCancelClick}
            className='w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors'
          >
            {getCancelText()}
          </button>
        </div>
      )}
    </div>
  );
};

const CancellationReason = ({
  bookingStatus,
  cancellationReason,
}: CancellationReasonProps) => {
  if (!isBookingCancelledOrDeclined(bookingStatus) || !cancellationReason) {
    return null;
  }

  return (
    <div className='flex items-start gap-2 mb-3'>
      <OctagonX className='w-5 h-5 text-[#EB001B] mt-0.5 flex-shrink-0' />
      <div className='text-sm font-medium text-gray-900'>
        {cancellationReason}
      </div>
    </div>
  );
};

const MessageButton = ({
  bookingStatus,
  unreadCount,
  onNavigate,
}: MessageButtonProps) => {
  if (!isBookingActiveOrCompleted(bookingStatus)) return null;

  const isCompleted = bookingStatus === 'Completed';
  const buttonClass = isCompleted
    ? 'text-gray-400 bg-gray-200 cursor-not-allowed'
    : 'text-[#FF8800] bg-[#FF88002E] shadow-lg';

  return (
    <div>
      <button
        className={`mt-3 flex gap-1 items-center px-4 py-2 rounded-lg font-semibold transition-colors whitespace-nowrap ${buttonClass}`}
        onClick={() => !isCompleted && onNavigate()}
        disabled={isCompleted}
      >
        <div className='relative'>
          <MessageCircle className='w-5 h-5' />
          <NotificationBadge count={unreadCount} className='-top-2 -right-2' />
        </div>
        Message
      </button>
    </div>
  );
};

const ViewMoreButton = ({
  bookingStatus,
  showTimeline,
  onToggleTimeline,
}: ViewMoreButtonProps) => {
  if (!isBookingActiveOrCompleted(bookingStatus)) return null;

  return (
    <div>
      <button
        className='mt-3 flex gap-1 items-center px-4 py-2 rounded-lg font-semibold text-[#7039D6] bg-[#7039D62E] transition-all duration-300 ease-in-out hover:bg-[#7039D640] whitespace-nowrap'
        onClick={onToggleTimeline}
      >
        <MoreVertical className='w-5 h-5' />
        {showTimeline ? 'View Less' : 'View More'}
      </button>
    </div>
  );
};

const PaymentButton = ({ bookingStatus, onNavigate }: PaymentButtonProps) => {
  if (bookingStatus !== 'Completed') return null;

  return (
    <button
      className='mt-3 w-full md:w-1/3 py-2 flex justify-center mx-auto rounded-lg font-semibold text-white bg-nursery-blue hover:bg-nursery-darkBlue transition-colors'
      onClick={onNavigate}
    >
      Make Payment
    </button>
  );
};

const TimelineStep = ({
  title,
  description,
  timestamp,
  isActive,
  showConnector,
  bgColor,
}: TimelineStepProps) => (
  <div className={`flex mt-6 ${isActive ? '' : 'opacity-50'}`}>
    <div className='relative flex flex-col items-center'>
      <div
        className={`w-6 h-6 rounded-full flex items-center justify-center z-10 ${isActive ? bgColor : 'bg-gray-300'}`}
      >
        <CheckCircle2 className='w-4 h-4 text-white' />
      </div>
      {showConnector && (
        <div
          className={`absolute top-6 h-full w-0.5 ${isActive ? bgColor.replace('bg-', 'bg-') : 'bg-gray-300'}`}
        ></div>
      )}
    </div>
    <div className='ml-3 flex-1'>
      <div className='font-semibold text-gray-800 text-base'>{title}</div>
      <div className='text-sm text-gray-600 mb-1'>{description}</div>
      {timestamp && (
        <div className='flex items-center gap-2 mb-3'>
          <p className='font-medium text-gray-500'>
            {formatDateShort(timestamp)}
          </p>
          <p className='font-medium text-gray-500'>
            {formatTime(new Date(timestamp).toTimeString().substring(0, 5))}
          </p>
        </div>
      )}
    </div>
  </div>
);

const ServiceTimeline = ({
  bookingStatus,
  showTimeline,
  booking,
  bookingServiceStatus,
  onNavigateToReview,
}: ServiceTimelineProps) => {
  if (!isBookingActiveOrCompleted(bookingStatus)) return null;

  const isStatusActive = () => {
    const activeStatuses = ['started', 'completed', 'payment_received'];
    return activeStatuses.includes(bookingServiceStatus?.status || '');
  };

  const isStatusCompleted = () => {
    const completedStatuses = ['completed', 'payment_received'];
    return completedStatuses.includes(bookingServiceStatus?.status || '');
  };

  const isPaymentReceived = () => {
    return bookingServiceStatus?.status === 'payment_received';
  };

  return (
    <div className='mt-4 overflow-hidden'>
      <AnimatePresence>
        {showTimeline && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className='mt-3 bg-white rounded-lg p-4'
          >
            <div className='space-y-0'>
              <TimelineStep
                title='Booking Confirmed'
                description='You have booked the Nurse'
                timestamp={booking.created_at}
                isActive={true}
                showConnector={isStatusActive()}
                bgColor='bg-nursery-blue'
              />

              <TimelineStep
                title='Service Started'
                description="It's the time since the nurse started providing the services"
                timestamp={bookingServiceStatus?.started_at}
                isActive={isStatusActive()}
                showConnector={isStatusCompleted()}
                bgColor='bg-[#00A3D3]'
              />

              <TimelineStep
                title='Service Completed'
                description='Service completed successfully'
                timestamp={bookingServiceStatus?.completed_at}
                isActive={isStatusCompleted()}
                showConnector={isPaymentReceived()}
                bgColor='bg-[#00A3D3]'
              />

              <TimelineStep
                title='Transaction Completed'
                description='You have made the payment'
                timestamp={bookingServiceStatus?.payment_received_at}
                isActive={isPaymentReceived()}
                showConnector={false}
                bgColor='bg-[#00A3D3]'
              />

              {isPaymentReceived() && (
                <ReviewSection onNavigate={onNavigateToReview} />
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

const ReviewSection = ({ onNavigate }: ReviewSectionProps) => (
  <div className='mt-4 pt-4 border-t border-gray-200'>
    <div className='flex items-center justify-between'>
      <div className='flex'>
        {[1, 2, 3, 4, 5].map(star => (
          <Star key={star} className='w-5 h-5 text-gray-300' />
        ))}
      </div>
      <button className='text-[#00A3D3] font-medium' onClick={onNavigate}>
        Write a review
      </button>
    </div>
  </div>
);

const NurseProfileImage = ({ profileImageUrl }: { profileImageUrl?: string }) => {
  if (!profileImageUrl) {
    return (
      <div className='w-10 h-10 md:w-10 md:h-10 bg-nursery-blue rounded-full flex items-center justify-center'>
        <User className='w-5 h-5 md:w-6 md:h-6 text-white' />
      </div>
    );
  }
  return (
    <div className='w-10 h-10 md:w-10 md:h-10 rounded-full overflow-hidden bg-nursery-blue flex items-center justify-center'>
      <img
        src={profileImageUrl}
        alt='Nurse profile'
        className='w-full h-full object-cover'
        onError={e => {
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
          const fallback = target.nextElementSibling as HTMLElement;
          if (fallback) {
            fallback.style.display = 'flex';
          }
        }}
      />
      <div
        className='w-full h-full bg-nursery-blue rounded-full flex items-center justify-center'
        style={{ display: 'none' }}
      >
        <User className='w-5 h-5 md:w-6 md:h-6 text-white' />
      </div>
    </div>
  );
};

const UpcomingSchedule = ({ booking }: UpcomingScheduleProps) => {
  const { unreadCount, refetch: _refetch } = useUnreadMessages();
  const navigate = useNavigate();
  const [showDropdown, setShowDropdown] = useState(false);
  const [showTimeline, setShowTimeline] = useState(false);
  const dropdownRef = useRef(null);
  const userId = localStorage.getItem('userId');

  const {
    data: serviceStatusData,
    error: serviceStatusError,
    isError: isServiceStatusError,
  } = useGetCustomerServiceStatusesQuery(userId, {
    skip: !userId || !shouldShowServiceStatus(booking?.booking_status),
  });

  useEffect(() => {
    if (isServiceStatusError && serviceStatusError) {
      const errorMessage =
        serviceStatusError?.data?.message || serviceStatusError?.message;
      const statusCode = serviceStatusError?.status;

      handleApiError(serviceStatusError, {
        403: 'Access denied. You can only view your own service statuses.',
        500: 'Failed to load service status. Please try again later.',
        [statusCode]: errorMessage || 'Failed to load service status.',
      });
    }
  }, [isServiceStatusError, serviceStatusError]);

  const bookingServiceStatus = getBookingServiceStatus(
    serviceStatusData,
    booking.booking_id
  );
  const badgeColor = getStatusColor(booking?.booking_status);

  const toggleTimeline = () => {
    setShowTimeline(!showTimeline);
  };

  useEffect(() => {
    const handleClickOutside = event => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleCancelClick = () => {
    setShowDropdown(false);
    navigate('/cancel-booking', {
      state: { booking },
    });
  };

  return (
    <div className='bg-[#F2F2F2] rounded-xl p-6'>
      <div className='flex flex-col gap-2'>
        <div className='flex items-center justify-between gap-2'>
          <StatusBadge
            bookingStatus={booking.booking_status}
            badgeColor={badgeColor}
          />
          <ActionDropdown
            bookingStatus={booking.booking_status}
            showDropdown={showDropdown}
            setShowDropdown={setShowDropdown}
            dropdownRef={dropdownRef}
            onCancelClick={handleCancelClick}
          />
        </div>
        <CancellationReason
          bookingStatus={booking.booking_status}
          cancellationReason={booking.cancellation_reason}
        />
        {}
        <div className='flex flex-col md:flex-row md:items-center gap-2 md:gap-5'>
          <div className='flex md:flex-row flex-col md:items-center items-start  gap-2 md:gap-6'>
            <div className='flex md:flex-row items-center md:ml-auto gap-1 md:gap-2 md:mt-0'>
              <p className='text-xs md:text-sm text-gray-500 font-medium'>
                Booking ID
              </p>
              <span className='text-sm md:text-base font-medium text-nursery-navy'>
                # {booking?.booking_id}
              </span>
            </div>
            <div className='flex md:gap-6 gap-2 mb-[3px]'>
              <div className='flex items-center gap-2'>
                <Calendar className='w-4 h-4 md:w-5 md:h-5 text-nursery-darkBlue' />
                <span className='text-sm md:text-base font-medium text-nursery-navy'>
                  {formatDate(booking.booked_date)}
                </span>
              </div>
              <div className='flex items-center gap-2'>
                <Clock className='w-4 h-4 md:w-5 md:h-5 text-nursery-darkBlue' />
                <span className='text-sm md:text-base font-medium text-nursery-navy'>
                  {}
                  {booking.booked_slot
                    .split(',')
                    .map((slot: string, idx: number, arr: string[]) => (
                      <span key={idx}>
                        {formatTime(slot.trim())}
                        {idx < arr.length - 1 ? ', ' : ''}
                      </span>
                    ))}
                </span>
              </div>
            </div>
          </div>
        </div>

        {}
        <div className='flex items-center justify-between gap-3'>
          <div className='flex gap-3'>
            <NurseProfileImage profileImageUrl={booking.profile_image_signed_url} />
            <div className='flex-1'>
              <p className='text-xs md:text-sm text-gray-500 font-medium'>
                Service Provider
              </p>
              <p className='text-sm md:text-base font-semibold text-nursery-navy'>
                {booking.nurse_given_name} {booking.nurse_family_name}
              </p>
            </div>
          </div>
        </div>
      </div>

      {}
      {booking?.nurse_location_address && (
        <div className='flex items-start gap-1 mt-3'>
          <MapPin className='w-4 h-4 text-nursery-darkBlue mt-0.5 flex-shrink-0' />
          <div>
            {}
            <p className='text-sm text-gray-700 leading-relaxed block md:hidden'>
              {getShortAddress(booking.nurse_location_address)}
            </p>
            {}
            <p className='text-sm text-gray-700 leading-relaxed hidden md:block'>
              {booking.nurse_location_address}
            </p>
          </div>
        </div>
      )}

      <div className='flex flex-row gap-4'>
        <MessageButton
          bookingStatus={booking.booking_status}
          unreadCount={unreadCount}
          onNavigate={() => navigate('/chat')}
        />
        <ViewMoreButton
          bookingStatus={booking.booking_status}
          showTimeline={showTimeline}
          onToggleTimeline={toggleTimeline}
        />
      </div>

      <PaymentButton
        bookingStatus={booking.booking_status}
        onNavigate={() => navigate('/payment', { state: { booking } })}
      />

      <ServiceTimeline
        bookingStatus={booking.booking_status}
        showTimeline={showTimeline}
        booking={booking}
        bookingServiceStatus={bookingServiceStatus}
        onNavigateToReview={() =>
          navigate('/write-review', { state: { booking } })
        }
      />
    </div>
  );
};

export default UpcomingSchedule;
